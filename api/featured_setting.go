package api

import (
	"net/http"

	"github.com/labstack/echo/v4"
)

// FeatureSettingData holds the combined settings data
type FeatureSettingData struct {
	Merchandise   interface{} `json:"merchandise"`
	Engrave       interface{} `json:"engrave"`
	Combo         interface{} `json:"combo"`
	Varian        interface{} `json:"varian"`
	PreOrder      interface{} `json:"pre_order"`
	Collection    interface{} `json:"collection"`
	Point         interface{} `json:"point"`
	Card          interface{} `json:"card"`
	Brand         interface{} `json:"brand"`
	Newsletter    interface{} `json:"newsletter"`
	Testimoni     interface{} `json:"testimoni"`
	Coupon        interface{} `json:"coupon"`
	Affiliate     interface{} `json:"affiliate"`
	GoldSaving    interface{} `json:"gold_saving"`
	TrustedAgent  interface{} `json:"trusted_agent"`
}

// FeatureSettingHandler handles /feature-setting endpoint
func FeatureSettingHandler() echo.HandlerFunc {
	return func(c echo.Context) error {
		data := FeatureSettingData{
			Merchandise:  settingMerchandise(),
			Engrave:      settingEngrave(),
			Combo:        settingCombo(),
			Varian:       settingVarian(),
			PreOrder:     settingPreOrder(),
			Collection:   settingCollection(),
			Point:        settingPoint(),
			Card:         settingCard(),
			Brand:        settingBrand(),
			Newsletter:   settingNewsletter(),
			Testimoni:    settingTestimoni(),
			Coupon:       settingCoupon(),
			Affiliate:    settingAffiliate(),
			GoldSaving:   settingGoldSaving(),
			TrustedAgent: settingTrustedAgent(),
		}

		return c.JSON(http.StatusOK, data)
	}
}

// Dummy example implementations of helper functions.
// Replace these with your actual logic fetching the data.

func settingMerchandise() interface{} {
	// your logic here
	return map[string]string{"example": "merchandise"}
}

func settingEngrave() interface{} {
	return map[string]string{"example": "engrave"}
}

func settingCombo() interface{} {
	return map[string]string{"example": "combo"}
}

func settingVarian() interface{} {
	return map[string]string{"example": "varian"}
}

func settingPreOrder() interface{} {
	return map[string]string{"example": "pre_order"}
}

func settingCollection() interface{} {
	return map[string]string{"example": "collection"}
}

func settingPoint() interface{} {
	return map[string]string{"example": "point"}
}

func settingCard() interface{} {
	return map[string]string{"example": "card"}
}

func settingBrand() interface{} {
	return map[string]string{"example": "brand"}
}

func settingNewsletter() interface{} {
	return map[string]string{"example": "newsletter"}
}

func settingTestimoni() interface{} {
	// You had this same as newsletter in your example
	return settingNewsletter()
}

func settingCoupon() interface{} {
	return map[string]string{"example": "coupon"}
}

func settingAffiliate() interface{} {
	return map[string]string{"example": "affiliate"}
}

func settingGoldSaving() interface{} {
	return map[string]string{"example": "gold_saving"}
}

func settingTrustedAgent() interface{} {
	return map[string]string{"example": "trusted_agent"}
}
